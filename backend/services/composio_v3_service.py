"""
Composio v3 SDK Integration Service

This service implements the new Composio v3 authentication flow using the updated SDK.
It replaces the previous MCP-based authentication with the cleaner v3 approach.

Flow:
1. Initialize Composio client with API key
2. Create auth config for the integration
3. Initiate OAuth connection request
4. Wait for connection establishment
5. Store connection in Supabase under agents as custom_mcp

New v3 Features:
- Clean SDK initialization with new Composio()
- composio.authConfigs.create() for auth config creation
- composio.connectedAccounts.initiate() for OAuth requests
- connRequest.waitForConnection() for connection establishment
- New callback URL: https://backend.composio.dev/api/v3/toolkits/callback
"""

import os
import json
import uuid
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from datetime import datetime, timedelta

from utils.logger import logger
from supabase import create_client, Client

# Supabase configuration
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_SERVICE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

# Composio v3 configuration
COMPOSIO_API_KEY = os.getenv("COMPOSIO_API_KEY")
COMPOSIO_CALLBACK_URL = "https://backend.composio.dev/api/v3/toolkits/callback"


@dataclass
class ComposioV3Connection:
    """Result of Composio v3 connection creation"""

    success: bool
    app_key: str
    integration_id: Optional[str] = None
    connection_id: Optional[str] = None
    redirect_url: Optional[str] = None
    auth_config_id: Optional[str] = None
    connection_request_id: Optional[str] = None
    error: Optional[str] = None
    message: Optional[str] = None


@dataclass
class ComposioV3AuthResult:
    """Result of Composio v3 authentication completion"""

    success: bool
    app_key: str
    connection_id: Optional[str] = None
    connected_account_id: Optional[str] = None
    toolsets: Optional[List[str]] = None
    error: Optional[str] = None


class ComposioV3Service:
    """Service for managing Composio v3 SDK integrations"""

    def __init__(self):
        if not SUPABASE_URL or not SUPABASE_SERVICE_KEY:
            raise ValueError("Supabase configuration missing")

        if not COMPOSIO_API_KEY:
            raise ValueError("Composio API key missing")

        self.supabase: Client = create_client(SUPABASE_URL, SUPABASE_SERVICE_KEY)
        self.composio = None
        self._init_composio_client()

    def _init_composio_client(self):
        """Initialize Composio v3 client"""
        try:
            # Import Composio v3 SDK
            from composio import Composio

            self.composio = Composio(api_key=COMPOSIO_API_KEY)
            logger.info("Successfully initialized Composio v3 client")
        except ImportError:
            logger.error(
                "Composio v3 SDK not installed. Please install with: pip install composio-core"
            )
            raise
        except Exception as e:
            logger.error(f"Failed to initialize Composio v3 client: {e}")
            raise

    def _load_integration_config(self, app_key: str) -> Optional[Dict[str, Any]]:
        """Load integration configuration from constants file"""
        try:
            constants_file = os.path.join(
                os.path.dirname(__file__),
                "..",
                "constants",
                "composio_mcp_servers.json",
            )

            with open(constants_file, "r") as f:
                constants = json.load(f)

            return constants.get(app_key)
        except Exception as e:
            logger.error(f"Failed to load integration config for {app_key}: {e}")
            return None

    async def create_auth_config(
        self, app_key: str, user_id: str
    ) -> ComposioV3Connection:
        """Create authentication configuration using Composio v3 SDK"""
        try:
            # Load integration configuration
            integration_config = self._load_integration_config(app_key)
            if not integration_config:
                return ComposioV3Connection(
                    success=False,
                    app_key=app_key,
                    error=f"Integration configuration not found for {app_key}",
                )

            integration_id = integration_config.get("integration_id")
            if not integration_id:
                return ComposioV3Connection(
                    success=False,
                    app_key=app_key,
                    error=f"Integration ID not found for {app_key}",
                )

            # Create auth config using v3 SDK
            auth_config = self.composio.authConfigs.create(
                {
                    "integrationId": integration_id,
                    "redirectUrl": COMPOSIO_CALLBACK_URL,
                    "metadata": {
                        "user_id": user_id,
                        "app_key": app_key,
                        "created_at": datetime.utcnow().isoformat(),
                    },
                }
            )

            logger.info(f"Created auth config for {app_key}: {auth_config.id}")

            return ComposioV3Connection(
                success=True,
                app_key=app_key,
                integration_id=integration_id,
                auth_config_id=auth_config.id,
                message=f"Auth config created for {app_key}",
            )

        except Exception as e:
            logger.error(f"Failed to create auth config for {app_key}: {e}")
            return ComposioV3Connection(success=False, app_key=app_key, error=str(e))

    async def initiate_connection(
        self, app_key: str, user_id: str
    ) -> ComposioV3Connection:
        """Initiate OAuth connection request using Composio v3 SDK"""
        try:
            # First create auth config
            auth_result = await self.create_auth_config(app_key, user_id)
            if not auth_result.success:
                return auth_result

            # Initiate connection request
            conn_request = self.composio.connectedAccounts.initiate(
                {
                    "integrationId": auth_result.integration_id,
                    "authConfig": auth_result.auth_config_id,
                    "redirectUrl": COMPOSIO_CALLBACK_URL,
                    "metadata": {"user_id": user_id, "app_key": app_key},
                }
            )

            logger.info(
                f"Initiated connection request for {app_key}: {conn_request.id}"
            )

            return ComposioV3Connection(
                success=True,
                app_key=app_key,
                integration_id=auth_result.integration_id,
                auth_config_id=auth_result.auth_config_id,
                connection_request_id=conn_request.id,
                redirect_url=conn_request.redirectUrl,
                message=f"Connection request initiated for {app_key}",
            )

        except Exception as e:
            logger.error(f"Failed to initiate connection for {app_key}: {e}")
            return ComposioV3Connection(success=False, app_key=app_key, error=str(e))

    async def wait_for_connection(
        self, connection_request_id: str, app_key: str, user_id: str, timeout: int = 300
    ) -> ComposioV3AuthResult:
        """Wait for connection establishment and return connection details"""
        try:
            # Get the connection request
            conn_request = self.composio.connectedAccounts.get(connection_request_id)

            # Wait for connection completion
            connection = conn_request.waitForConnection(timeout=timeout)

            if connection and connection.id:
                logger.info(f"Connection established for {app_key}: {connection.id}")

                # Get available toolsets for this connection
                toolsets = await self._get_connection_toolsets(connection.id, app_key)

                # Store the connection in Supabase
                stored = await self._store_connection_in_supabase(
                    user_id=user_id,
                    app_key=app_key,
                    connection_id=connection.id,
                    connected_account_id=connection.connectedAccountId,
                    toolsets=toolsets,
                )

                if stored:
                    return ComposioV3AuthResult(
                        success=True,
                        app_key=app_key,
                        connection_id=connection.id,
                        connected_account_id=connection.connectedAccountId,
                        toolsets=toolsets,
                    )
                else:
                    return ComposioV3AuthResult(
                        success=False,
                        app_key=app_key,
                        error="Failed to store connection in database",
                    )
            else:
                return ComposioV3AuthResult(
                    success=False,
                    app_key=app_key,
                    error="Connection not established within timeout period",
                )

        except Exception as e:
            logger.error(f"Failed to wait for connection {connection_request_id}: {e}")
            return ComposioV3AuthResult(success=False, app_key=app_key, error=str(e))

    async def _get_connection_toolsets(
        self, connection_id: str, app_key: str
    ) -> List[str]:
        """Get available toolsets for a connection"""
        try:
            # Load integration config to get available toolsets
            integration_config = self._load_integration_config(app_key)
            if not integration_config:
                return []

            # For now, return default toolsets based on app
            # In production, you might want to query the actual connection
            default_toolsets = {
                "gmail": [
                    "GMAIL_SEND_EMAIL",
                    "GMAIL_FETCH_EMAILS",
                    "GMAIL_CREATE_DRAFT",
                ],
                "notion": ["NOTION_CREATE_PAGE", "NOTION_UPDATE_PAGE", "NOTION_SEARCH"],
            }

            return default_toolsets.get(app_key, [])

        except Exception as e:
            logger.error(f"Failed to get toolsets for connection {connection_id}: {e}")
            return []

    async def _store_connection_in_supabase(
        self,
        user_id: str,
        app_key: str,
        connection_id: str,
        connected_account_id: str,
        toolsets: List[str],
    ) -> bool:
        """Store the v3 connection in Supabase under agents as custom_mcp"""
        try:
            # Get or create default agent for user
            agent = await self._get_or_create_default_agent(user_id)
            if not agent:
                logger.error(f"Failed to get/create default agent for user {user_id}")
                return False

            # Load integration config
            integration_config = self._load_integration_config(app_key)
            if not integration_config:
                logger.error(f"Integration config not found for {app_key}")
                return False

            # Create custom MCP entry for the v3 connection
            custom_mcp_entry = {
                "qualified_name": f"composio/{app_key}",
                "server_url": f"https://backend.composio.dev/api/v3/connectedAccounts/{connection_id}/mcp",
                "server_id": integration_config.get("server_id"),
                "integration_id": integration_config.get("integration_id"),
                "connection_id": connection_id,
                "connected_account_id": connected_account_id,
                "app_key": app_key,
                "app_name": integration_config.get("name", app_key.title()),
                "description": integration_config.get(
                    "description", f"{app_key.title()} integration"
                ),
                "toolsets": toolsets,
                "auth_type": "composio_v3",
                "created_at": datetime.utcnow().isoformat(),
                "updated_at": datetime.utcnow().isoformat(),
            }

            # Get existing custom MCPs
            existing_mcps = agent.get("custom_mcps", [])
            if not isinstance(existing_mcps, list):
                existing_mcps = []

            # Remove existing connection for same app_key if exists
            existing_mcps = [
                mcp for mcp in existing_mcps if mcp.get("app_key") != app_key
            ]

            # Add new connection
            existing_mcps.append(custom_mcp_entry)

            # Update agent with new custom MCPs
            result = (
                self.supabase.table("agents")
                .update(
                    {
                        "custom_mcps": existing_mcps,
                        "updated_at": datetime.utcnow().isoformat(),
                    }
                )
                .eq("id", agent["id"])
                .execute()
            )

            if result.data:
                logger.info(
                    f"Successfully stored v3 connection for {app_key} in Supabase"
                )
                return True
            else:
                logger.error(f"Failed to update agent with v3 connection for {app_key}")
                return False

        except Exception as e:
            logger.error(f"Failed to store v3 connection in Supabase: {e}")
            return False

    async def _get_or_create_default_agent(
        self, user_id: str
    ) -> Optional[Dict[str, Any]]:
        """Get or create default agent for user"""
        try:
            # First try to get existing default agent
            result = (
                self.supabase.table("agents")
                .select("*")
                .eq("user_id", user_id)
                .eq("is_default", True)
                .execute()
            )

            if result.data:
                return result.data[0]

            # If no default agent exists, create one
            from utils.default_agent_config import get_default_agent_config

            default_config = get_default_agent_config()
            agent_data = {
                "id": str(uuid.uuid4()),
                "user_id": user_id,
                "name": "Default Agent",
                "description": "Default agent for MCP integrations",
                "is_default": True,
                "custom_mcps": [],
                "created_at": datetime.utcnow().isoformat(),
                "updated_at": datetime.utcnow().isoformat(),
                **default_config,
            }

            result = self.supabase.table("agents").insert(agent_data).execute()

            if result.data:
                logger.info(f"Created default agent for user {user_id}")
                return result.data[0]
            else:
                logger.error(f"Failed to create default agent for user {user_id}")
                return None

        except Exception as e:
            logger.error(f"Failed to get/create default agent for user {user_id}: {e}")
            return None

    async def list_user_connections(self, user_id: str) -> List[Dict[str, Any]]:
        """List all Composio v3 connections for a user"""
        try:
            # Get user's default agent
            agent = await self._get_or_create_default_agent(user_id)
            if not agent:
                return []

            # Filter custom MCPs for Composio v3 connections
            custom_mcps = agent.get("custom_mcps", [])
            if not isinstance(custom_mcps, list):
                return []

            v3_connections = [
                mcp for mcp in custom_mcps if mcp.get("auth_type") == "composio_v3"
            ]

            logger.info(
                f"Found {len(v3_connections)} v3 connections for user {user_id}"
            )
            return v3_connections

        except Exception as e:
            logger.error(f"Failed to list user v3 connections: {e}")
            return []

    async def delete_user_connection(self, user_id: str, app_key: str) -> bool:
        """Delete a Composio v3 connection for a user"""
        try:
            # Get user's default agent
            agent = await self._get_or_create_default_agent(user_id)
            if not agent:
                return False

            # Remove the connection from custom MCPs
            custom_mcps = agent.get("custom_mcps", [])
            if not isinstance(custom_mcps, list):
                custom_mcps = []

            updated_mcps = [mcp for mcp in custom_mcps if mcp.get("app_key") != app_key]

            # Update agent
            result = (
                self.supabase.table("agents")
                .update(
                    {
                        "custom_mcps": updated_mcps,
                        "updated_at": datetime.utcnow().isoformat(),
                    }
                )
                .eq("id", agent["id"])
                .execute()
            )

            if result.data:
                logger.info(f"Successfully deleted v3 connection for {app_key}")
                return True
            else:
                logger.error(f"Failed to delete v3 connection for {app_key}")
                return False

        except Exception as e:
            logger.error(f"Failed to delete user v3 connection: {e}")
            return False

    async def get_supported_integrations(self) -> List[Dict[str, Any]]:
        """Get list of supported integrations from constants file"""
        try:
            constants_file = os.path.join(
                os.path.dirname(__file__),
                "..",
                "constants",
                "composio_mcp_servers.json",
            )

            with open(constants_file, "r") as f:
                constants = json.load(f)

            integrations = []
            for app_key, config in constants.items():
                integrations.append(
                    {
                        "app_key": app_key,
                        "name": config.get("name", app_key.title()),
                        "description": config.get(
                            "description", f"{app_key.title()} integration"
                        ),
                        "integration_id": config.get("integration_id"),
                        "server_id": config.get("server_id"),
                    }
                )

            return integrations

        except Exception as e:
            logger.error(f"Failed to get supported integrations: {e}")
            return []


# Global service instance
composio_v3_service = ComposioV3Service()
