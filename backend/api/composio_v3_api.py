"""
Composio v3 SDK API Endpoints

This module provides FastAPI endpoints for the new Composio v3 authentication flow.
It replaces the previous MCP-based authentication with the cleaner v3 SDK approach.

Key differences from the old implementation:
- Uses Composio v3 SDK for authentication
- Cleaner connection initiation process
- Stores connections directly in Supabase agents table
- Uses new callback URL: https://backend.composio.dev/api/v3/toolkits/callback
"""

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from pydantic import BaseModel
from typing import Dict, Any, Optional, List
from utils.auth_utils import get_current_user_id_from_jwt
from utils.logger import logger
from services.composio_v3_service import composio_v3_service

router = APIRouter(prefix="/composio-v3", tags=["composio-v3"])


class InitiateV3ConnectionRequest(BaseModel):
    """Request model for initiating a Composio v3 connection"""

    app_key: str  # e.g., "gmail", "notion"


class InitiateV3ConnectionResponse(BaseModel):
    """Response model for v3 connection initiation"""

    success: bool
    app_key: str
    redirect_url: Optional[str] = None
    connection_request_id: Optional[str] = None
    integration_id: Optional[str] = None
    auth_config_id: Optional[str] = None
    message: str
    error: Optional[str] = None


class ConnectionStatusRequest(BaseModel):
    """Request model for checking connection status"""

    connection_request_id: str
    app_key: str


class ConnectionStatusResponse(BaseModel):
    """Response model for connection status"""

    success: bool
    app_key: str
    connection_id: Optional[str] = None
    connected_account_id: Optional[str] = None
    toolsets: Optional[List[str]] = None
    is_connected: bool
    message: str
    error: Optional[str] = None


class ListV3ConnectionsResponse(BaseModel):
    """Response model for listing v3 connections"""

    success: bool
    connections: List[Dict[str, Any]]
    total: int
    message: str


class DeleteV3ConnectionResponse(BaseModel):
    """Response model for deleting v3 connection"""

    success: bool
    app_key: str
    message: str
    error: Optional[str] = None


class SupportedIntegrationsResponse(BaseModel):
    """Response model for supported integrations"""

    success: bool
    integrations: List[Dict[str, Any]]
    total: int
    message: str


@router.post("/initiate-connection", response_model=InitiateV3ConnectionResponse)
async def initiate_v3_connection(
    request: InitiateV3ConnectionRequest,
    background_tasks: BackgroundTasks,
    user_id: str = Depends(get_current_user_id_from_jwt),
):
    """
    Initiate a new Composio v3 connection using the SDK.

    This endpoint:
    1. Creates an auth config for the integration
    2. Initiates an OAuth connection request
    3. Returns a redirect URL for user authentication
    4. Optionally starts background task to wait for connection completion

    Args:
        request: Contains the app_key (e.g., "gmail", "notion")
        user_id: Current user ID from JWT token
        background_tasks: FastAPI background tasks for async processing

    Returns:
        Response with redirect_url for frontend to redirect user to
    """
    try:
        logger.info(
            f"Initiating Composio v3 connection for user {user_id}, app {request.app_key}"
        )

        # Initiate the connection using v3 service
        connection_result = await composio_v3_service.initiate_connection(
            app_key=request.app_key, user_id=user_id
        )

        if connection_result.success:
            logger.info(
                f"Successfully initiated v3 connection for {request.app_key}: {connection_result.connection_request_id}"
            )

            return InitiateV3ConnectionResponse(
                success=True,
                app_key=connection_result.app_key,
                redirect_url=connection_result.redirect_url,
                connection_request_id=connection_result.connection_request_id,
                integration_id=connection_result.integration_id,
                auth_config_id=connection_result.auth_config_id,
                message=f"Connection initiated for {request.app_key}. Please complete authentication at the provided URL.",
            )
        else:
            logger.error(f"Failed to initiate v3 connection: {connection_result.error}")
            return InitiateV3ConnectionResponse(
                success=False,
                app_key=connection_result.app_key,
                error=connection_result.error,
                message=f"Failed to initiate connection for {request.app_key}",
            )

    except Exception as e:
        logger.error(f"Error in initiate_v3_connection: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/check-connection-status", response_model=ConnectionStatusResponse)
async def check_connection_status(
    request: ConnectionStatusRequest,
    user_id: str = Depends(get_current_user_id_from_jwt),
):
    """
    Check the status of a connection request and wait for completion.

    This endpoint will wait for the user to complete the OAuth flow
    and return the connection details once established.

    Args:
        request: Contains connection_request_id and app_key
        user_id: Current user ID from JWT token

    Returns:
        Connection status and details if connected
    """
    try:
        logger.info(
            f"Checking connection status for user {user_id}, request {request.connection_request_id}"
        )

        # Wait for connection completion (with reasonable timeout)
        auth_result = await composio_v3_service.wait_for_connection(
            connection_request_id=request.connection_request_id,
            app_key=request.app_key,
            user_id=user_id,
            timeout=60,  # 1 minute timeout for API call
        )

        if auth_result.success:
            logger.info(
                f"Connection established for {request.app_key}: {auth_result.connection_id}"
            )

            return ConnectionStatusResponse(
                success=True,
                app_key=auth_result.app_key,
                connection_id=auth_result.connection_id,
                connected_account_id=auth_result.connected_account_id,
                toolsets=auth_result.toolsets,
                is_connected=True,
                message=f"Connection successfully established for {request.app_key}",
            )
        else:
            logger.warning(f"Connection not yet established: {auth_result.error}")
            return ConnectionStatusResponse(
                success=False,
                app_key=auth_result.app_key,
                is_connected=False,
                error=auth_result.error,
                message="Connection not yet established or failed",
            )

    except Exception as e:
        logger.error(f"Error checking connection status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/user-connections", response_model=ListV3ConnectionsResponse)
async def list_user_v3_connections(
    user_id: str = Depends(get_current_user_id_from_jwt),
):
    """
    List all Composio v3 connections for the current user.

    Returns connections stored in the agents table with auth_type="composio_v3".
    """
    try:
        logger.info(f"Listing Composio v3 connections for user {user_id}")

        connections = await composio_v3_service.list_user_connections(user_id)

        return ListV3ConnectionsResponse(
            success=True,
            connections=connections,
            total=len(connections),
            message=f"Found {len(connections)} v3 connections",
        )

    except Exception as e:
        logger.error(f"Error listing user v3 connections: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/connection/{app_key}", response_model=DeleteV3ConnectionResponse)
async def delete_v3_connection(
    app_key: str,
    user_id: str = Depends(get_current_user_id_from_jwt),
):
    """
    Delete a Composio v3 connection for the current user.

    This removes the connection from the agents table custom_mcps.
    """
    try:
        logger.info(f"Deleting v3 connection for user {user_id}, app {app_key}")

        success = await composio_v3_service.delete_user_connection(user_id, app_key)

        if success:
            return DeleteV3ConnectionResponse(
                success=True,
                app_key=app_key,
                message=f"Successfully deleted connection for {app_key}",
            )
        else:
            return DeleteV3ConnectionResponse(
                success=False,
                app_key=app_key,
                error="Failed to delete connection",
                message=f"Failed to delete connection for {app_key}",
            )

    except Exception as e:
        logger.error(f"Error deleting v3 connection: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/supported-integrations", response_model=SupportedIntegrationsResponse)
async def get_supported_integrations():
    """
    Get list of all supported Composio integrations.

    Returns integrations defined in the constants file with their
    integration IDs and metadata.
    """
    try:
        logger.info("Fetching supported v3 integrations")

        integrations = await composio_v3_service.get_supported_integrations()

        return SupportedIntegrationsResponse(
            success=True,
            integrations=integrations,
            total=len(integrations),
            message=f"Found {len(integrations)} supported integrations",
        )

    except Exception as e:
        logger.error(f"Error fetching supported integrations: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health")
async def health_check():
    """Health check endpoint for v3 API"""
    return {
        "status": "healthy",
        "service": "composio-v3-api",
        "version": "1.0.0",
        "timestamp": logger.info("V3 API health check"),
    }


# Background task for connection completion
async def complete_connection_background(
    connection_request_id: str, app_key: str, user_id: str, timeout: int = 300
):
    """
    Background task to wait for connection completion.

    This can be used to automatically complete the connection process
    without requiring the frontend to poll for status.
    """
    try:
        logger.info(f"Starting background connection completion for {app_key}")

        auth_result = await composio_v3_service.wait_for_connection(
            connection_request_id=connection_request_id,
            app_key=app_key,
            user_id=user_id,
            timeout=timeout,
        )

        if auth_result.success:
            logger.info(
                f"Background connection completed for {app_key}: {auth_result.connection_id}"
            )
        else:
            logger.warning(
                f"Background connection failed for {app_key}: {auth_result.error}"
            )

    except Exception as e:
        logger.error(f"Error in background connection completion: {e}")


@router.post("/wait-for-connection")
async def start_connection_wait(
    request: ConnectionStatusRequest,
    background_tasks: BackgroundTasks,
    user_id: str = Depends(get_current_user_id_from_jwt),
):
    """
    Start a background task to wait for connection completion.

    This is an alternative to polling that uses background processing.
    """
    try:
        background_tasks.add_task(
            complete_connection_background,
            request.connection_request_id,
            request.app_key,
            user_id,
        )

        return {
            "success": True,
            "message": "Background connection wait started",
            "connection_request_id": request.connection_request_id,
        }

    except Exception as e:
        logger.error(f"Error starting background connection wait: {e}")
        raise HTTPException(status_code=500, detail=str(e))
