'use client';

import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from 'sonner';
import {
  Search,
  Loader2,
  Check,
  X
} from 'lucide-react';
import { ComposioApp } from '@/types/composio';
import { createClient } from '@/lib/supabase/client';
import { MCPServerCard } from './mcp-server-card';
import { useQueryClient } from '@tanstack/react-query';
import { agentKeys } from '@/hooks/react-query/agents/keys';

interface MCPServerCarouselProps {
  className?: string;
}

export function MCPServerCarousel({ className }: MCPServerCarouselProps) {
  const queryClient = useQueryClient();
  const [apps, setApps] = useState<ComposioApp[]>([]);
  const [filteredApps, setFilteredApps] = useState<ComposioApp[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [showSearch, setShowSearch] = useState(false);
  const [connectingApps, setConnectingApps] = useState<Set<string>>(new Set());
  const [disconnectingApps, setDisconnectingApps] = useState<Set<string>>(new Set());
  const [connectedApps, setConnectedApps] = useState<Set<string>>(new Set());
  const [pendingConnections, setPendingConnections] = useState<Map<string, string>>(new Map()); // appKey -> connection_request_id

  // Helper function to get authenticated headers
  const getAuthHeaders = async () => {
    const supabase = createClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session?.access_token) {
      throw new Error('No authentication token available. Please sign in.');
    }

    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${session.access_token}`,
    };
  };

  // Load supported apps
  useEffect(() => {
    const loadApps = async () => {
      try {
        setLoading(true);

        const API_URL = process.env.NEXT_PUBLIC_API_URL || process.env.NEXT_PUBLIC_BACKEND_URL?.replace(/\/api$/, '') || '';

        const response = await fetch(`${API_URL}/api/composio-v3/supported-integrations`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        if (data.success) {
          setApps(data.integrations);
          setFilteredApps(data.integrations);
        } else {
          throw new Error(data.message || 'Failed to load integrations');
        }
      } catch (error) {
        console.error('Error loading apps:', error);
        toast.error("Failed to load MCP servers", {
          description: error instanceof Error ? error.message : 'Unknown error',
        });
      } finally {
        setLoading(false);
      }
    };

    loadApps();
  }, []);

  // Load existing connections and handle post-OAuth refresh
  useEffect(() => {
    const loadConnections = async () => {
      try {
        const authHeaders = await getAuthHeaders();
        const API_URL = process.env.NEXT_PUBLIC_API_URL || process.env.NEXT_PUBLIC_BACKEND_URL?.replace(/\/api$/, '') || '';

        const response = await fetch(`${API_URL}/api/composio-v3/user-connections`, {
          method: 'GET',
          headers: authHeaders,
        });

        if (response.ok) {
          const data = await response.json();
          if (data.success) {
            const connectedAppKeys: Set<string> = new Set(data.connections.map((conn: any) => conn.app_key as string));
            setConnectedApps(connectedAppKeys);
          }
        }
      } catch (error) {
        console.error('Error loading connections:', error);
      }
    };

    // Check if user just returned from OAuth authentication
    const handlePostOAuthRefresh = async () => {
      const recentlyConnectedKey = localStorage.getItem('composio_recently_connected');
      const connectionRequestId = localStorage.getItem('composio_connection_request_id');

      if (recentlyConnectedKey && connectionRequestId) {
        console.log(`Checking connection status for ${recentlyConnectedKey}...`);

        try {
          // Check connection status
          await checkConnectionStatus(recentlyConnectedKey, connectionRequestId);

          // Clear the flags
          localStorage.removeItem('composio_recently_connected');
          localStorage.removeItem('composio_connection_request_id');

          // Reload connections to show updated state
          await loadConnections();

          // Invalidate React Query cache to refresh cursor agent selector
          queryClient.invalidateQueries({ queryKey: agentKeys.lists() });
          queryClient.invalidateQueries({ queryKey: agentKeys.defaultMCPs() });

          toast.success("Authentication Complete!", {
            description: `${recentlyConnectedKey} is now connected and ready to use.`,
          });

        } catch (error) {
          console.error('Error checking connection status after OAuth:', error);
          toast.error("Connection Check Failed", {
            description: "Please try refreshing the page if the connection doesn't appear.",
          });
        }
      }
    };

    loadConnections();
    handlePostOAuthRefresh();

    // Listen for window focus to detect when user returns from OAuth
    const handleWindowFocus = () => {
      const recentlyConnectedKey = localStorage.getItem('composio_recently_connected');
      const connectionRequestId = localStorage.getItem('composio_connection_request_id');
      if (recentlyConnectedKey && connectionRequestId) {
        console.log('Window focused and recently connected app detected, checking status...');
        handlePostOAuthRefresh();
      }
    };

    window.addEventListener('focus', handleWindowFocus);

    return () => {
      window.removeEventListener('focus', handleWindowFocus);
    };
  }, [queryClient]);

  // Check connection status helper function
  const checkConnectionStatus = async (appKey: string, connectionRequestId: string) => {
    const API_URL = process.env.NEXT_PUBLIC_API_URL || process.env.NEXT_PUBLIC_BACKEND_URL?.replace(/\/api$/, '') || '';
    const authHeaders = await getAuthHeaders();

    const response = await fetch(`${API_URL}/api/composio-v3/check-connection-status`, {
      method: 'POST',
      headers: authHeaders,
      body: JSON.stringify({
        connection_request_id: connectionRequestId,
        app_key: appKey,
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to check connection status');
    }

    const data = await response.json();

    if (data.success && data.connected) {
      setConnectedApps(prev => new Set(prev).add(appKey));
      setPendingConnections(prev => {
        const newMap = new Map(prev);
        newMap.delete(appKey);
        return newMap;
      });
      return true;
    }

    return false;
  };

  // Filter and sort apps (connected first)
  useEffect(() => {
    let filtered = apps;

    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter(app =>
        app.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        app.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        app.key.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Sort: connected apps first, then by popularity/name
    filtered.sort((a, b) => {
      const aConnected = connectedApps.has(a.key);
      const bConnected = connectedApps.has(b.key);

      if (aConnected && !bConnected) return -1;
      if (!aConnected && bConnected) return 1;

      // If both connected or both not connected, sort by popularity then name
      if (a.popular && !b.popular) return -1;
      if (!a.popular && b.popular) return 1;

      return a.name.localeCompare(b.name);
    });

    setFilteredApps(filtered);
  }, [apps, searchQuery, connectedApps]);

  // Handle search toggle
  const handleSearchToggle = () => {
    if (showSearch && searchQuery) {
      setSearchQuery('');
    }
    setShowSearch(!showSearch);
  };

  if (loading) {
    return (
      <div className={className}>
        <div className="flex justify-end items-center mb-2">
          <Skeleton className="h-8 w-16" />
        </div>
        <div className="flex gap-4 overflow-x-auto">
          {Array.from({ length: 6 }).map((_, i) => (
            <Skeleton key={i} className="h-14 w-44 rounded-lg flex-shrink-0" />
          ))}
        </div>
      </div>
    );
  }

  // Don't render if no apps are available
  if (apps.length === 0) {
    return null;
  }

  return (
    <div className={className}>
      {/* Header with search button - matching suggestions style */}
      <div className="flex justify-end items-center mb-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleSearchToggle}
          className="h-8 px-3 text-sm text-muted-foreground hover:text-foreground"
        >
          <motion.div
            animate={{ rotate: showSearch ? 180 : 0 }}
            transition={{ duration: 0.2 }}
          >
            {showSearch && searchQuery ? <X size={14} /> : <Search size={14} />}
          </motion.div>
          <span className="ml-2">Search</span>
        </Button>
      </div>

      {/* Search input */}
      <AnimatePresence>
        {showSearch && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.2 }}
            className="mb-2"
          >
            <Input
              placeholder="Search MCP servers..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="h-8 text-sm"
              autoFocus
            />
          </motion.div>
        )}
      </AnimatePresence>

      {/* Cards container */}
      <div className="flex gap-4 overflow-x-auto scrollbar-hide">
        {filteredApps.map((app) => (
          <MCPServerCard
            key={app.key}
            app={app}
            isConnected={connectedApps.has(app.key)}
            isConnecting={connectingApps.has(app.key)}
            onConnect={() => handleConnect(app.key, app.name)}
            onDisconnect={() => handleDisconnect(app.key, app.name)}
            // Remove onViewTools since v3 doesn't need tool selection
          />
        ))}
      </div>

      {/* Results info */}
      {searchQuery && (
        <div className="text-xs text-muted-foreground mt-2">
          {filteredApps.length} results for "{searchQuery}"
        </div>
      )}
    </div>
  );

  // Handle MCP server connection - simplified v3 flow
  async function handleConnect(appKey: string, appName: string) {
    if (connectingApps.has(appKey) || connectedApps.has(appKey)) return;

    setConnectingApps(prev => new Set(prev).add(appKey));

    try {
      const API_URL = process.env.NEXT_PUBLIC_API_URL || process.env.NEXT_PUBLIC_BACKEND_URL?.replace(/\/api$/, '') || '';
      const authHeaders = await getAuthHeaders();

      // Initiate connection - this returns redirect URL and connection_request_id
      const response = await fetch(`${API_URL}/api/composio-v3/initiate-connection`, {
        method: 'POST',
        headers: authHeaders,
        body: JSON.stringify({ app_key: appKey }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to initiate connection: ${errorText}`);
      }

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.message || 'Failed to initiate connection');
      }

      if (data.redirect_url && data.connection_request_id) {
        // Store info for post-OAuth status check
        localStorage.setItem('composio_recently_connected', appKey);
        localStorage.setItem('composio_connection_request_id', data.connection_request_id);

        // Store pending connection
        setPendingConnections(prev => new Map(prev).set(appKey, data.connection_request_id));

        toast.success("Redirecting to Authentication", {
          description: `Opening ${appName} authentication...`,
        });

        // Redirect to OAuth
        window.open(data.redirect_url, '_blank');
      } else {
        toast.success("Integration Connected!", {
          description: `${appName} has been successfully connected.`,
        });

        // Mark as connected immediately
        setConnectedApps(prev => new Set(prev).add(appKey));
      }

    } catch (error: any) {
      console.error('Connection error:', error);

      if (error.message?.includes('401') || error.message?.includes('authentication') || error.message?.includes('No authentication token')) {
        toast.error("Authentication Required", {
          description: "Please sign in to connect MCP servers",
        });
      } else {
        toast.error("Connection Failed", {
          description: error.message || `Failed to connect to ${appName}`,
        });
      }
    } finally {
      setConnectingApps(prev => {
        const newSet = new Set(prev);
        newSet.delete(appKey);
        return newSet;
      });
    }
  }

  // Handle MCP server disconnection
  async function handleDisconnect(appKey: string, appName: string) {
    if (disconnectingApps.has(appKey) || !connectedApps.has(appKey)) return;

    setDisconnectingApps(prev => new Set(prev).add(appKey));

    try {
      const API_URL = process.env.NEXT_PUBLIC_API_URL || process.env.NEXT_PUBLIC_BACKEND_URL?.replace(/\/api$/, '') || '';
      const authHeaders = await getAuthHeaders();

      // Call the delete endpoint to remove the v3 connection
      const response = await fetch(`${API_URL}/api/composio-v3/connection/${appKey}`, {
        method: 'DELETE',
        headers: authHeaders,
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to disconnect: ${errorText}`);
      }

      const data = await response.json();

      if (data.success) {
        // Remove from connected apps
        setConnectedApps(prev => {
          const newSet = new Set(prev);
          newSet.delete(appKey);
          return newSet;
        });

        // Remove from pending connections if any
        setPendingConnections(prev => {
          const newMap = new Map(prev);
          newMap.delete(appKey);
          return newMap;
        });

        // Invalidate React Query cache to refresh cursor agent selector
        queryClient.invalidateQueries({ queryKey: agentKeys.lists() });
        queryClient.invalidateQueries({ queryKey: agentKeys.defaultMCPs() });

        toast.success("Disconnected Successfully", {
          description: `${appName} has been disconnected and removed.`,
        });
      } else {
        throw new Error(data.message || 'Failed to disconnect');
      }

    } catch (error: any) {
      console.error('Disconnect error:', error);
      toast.error("Disconnect Failed", {
        description: error.message || `Failed to disconnect ${appName}`,
      });
    } finally {
      setDisconnectingApps(prev => {
        const newSet = new Set(prev);
        newSet.delete(appKey);
        return newSet;
      });
    }
  }
}
